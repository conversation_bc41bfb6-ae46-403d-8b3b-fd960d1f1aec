#include "monitoring_datasource.h"
#include <QVariantMap>
#include <QtCharts/QXYSeries>
#include <QtCharts/QLineSeries>
#include <QCoreApplication>
#include <QtCharts/QAbstractSeries>
#include <QDateTime>
#include <QTimer>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QMetaObject>
#include <thread>
#include <chrono>

// Qt 5.x需要使用QtCharts命名空间
QT_CHARTS_USE_NAMESPACE

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// BoilerSwitchTask类已移除，因为使用简化的锅炉切换逻辑

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#ifdef ENABLE_HARDWARE_DATA
#include "smoke_analyzer_comm.h"
#include "boiler.h"
#include "config_manager.h"
#endif

MonitoringDataSource::MonitoringDataSource(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_csvReader(new CsvReader(this))
    , m_tableUpdateCounter(0)
    , m_isBackflowActive(false)
    , m_isDataUpdateSuspended(false)
    , m_backflowDelayTimer(new QTimer(this))
    , m_backflowDelayTime(60)
    , m_suspendedO2Value("0.00%")
    , m_suspendedCOValue("0ppm")
    , m_isRunning(false)
    , m_isDataConnected(false)
    , m_connectionStatus("未连接串口数据采集设备")
    , m_dataCount(0)
    , m_currentTemperature("0.0℃")
    , m_currentVoltage("0.0kPa")
    , m_currentCurrent("0.000A")
    , m_dataStartTimeSet(false)
    , m_redisClient(nullptr)
    , m_redisEnabled(false)
    , m_redisHost("127.0.0.1")
    , m_redisPort(6379)
    , m_redisPassword("")
    , m_redisDatabase(0)
    , m_redisCacheExpireHours(24)
{
    connect(m_timer, &QTimer::timeout, this, &MonitoringDataSource::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 初始化反吹反馈延迟定时器
    m_backflowDelayTimer->setSingleShot(true);
    connect(m_backflowDelayTimer, &QTimer::timeout, this, &MonitoringDataSource::resumeO2COUpdates);

    // 加载锅炉列表
    loadBoilerList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();

    // 初始化Redis缓存
    initializeRedis();
}

void MonitoringDataSource::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void MonitoringDataSource::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("切换烟气分析仪: 从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        // 简化的烟气分析仪切换逻辑，因为每个设备都有独立线程在采集数据
        m_currentBoiler = boiler;

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 清空表格数据，准备显示新设备的数据（CSV数据保持在文件中）
        m_smokeTableData.clear();
        m_dataCount = 0;
        m_tableUpdateCounter = 0;  // 重置表格更新计数器

        // 重置相对时间轴的开始时间
        m_dataStartTimeSet = false;

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "正在检测新烟气分析仪的串口连接...";
        emit dataConnectionChanged();

        // 发射数据变化信号（保留表格相关信号，移除图表更新信号）
        emit smokeDataChanged();
        emit smokeTableDataChanged();
        // 移除 chartDataUpdated 信号，改为QML主动请求模式
        debug_printf("监控系统: 锅炉切换完成\n");

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            updateData();
        }
    }
}

void MonitoringDataSource::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("UI监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据，确保图表能正常显示
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("UI监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
        });
    }
}



void MonitoringDataSource::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();

        // 停止监控时重置连接状态
        m_isDataConnected = false;
        m_connectionStatus = "数据监控已停止";
        emit dataConnectionChanged();
    }
}

int MonitoringDataSource::getCurrentCollectionInterval() const
{
    // 获取当前锅炉的采集间隔
    int interval = 15;  // 默认值

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            interval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    return interval;
}

int MonitoringDataSource::getBackflowDelayTime() const
{
    // 从配置文件读取反吹反馈延迟时间
    int delayTime = 60;  // 默认60秒

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            delayTime = g_config_manager->get<int>(m_currentBoiler.toStdString(), "BackflowDelayTime", 60);
        }
    }

    return delayTime;
}

void MonitoringDataSource::checkBackflowStatus(int switch1)
{
    bool currentBackflowActive = (switch1 == 1);  // 修正：1表示反吹运行，0表示停止

    // 检查反吹反馈状态是否发生变化
    if (currentBackflowActive != m_isBackflowActive) {
        m_isBackflowActive = currentBackflowActive;

        if (m_isBackflowActive) {
            // 反吹反馈开始运行，暂停氧气和一氧化碳数值更新
            suspendO2COUpdates();
            debug_printf("监控系统: 检测到反吹反馈开始运行，暂停氧气和一氧化碳数值更新\n");
        } else {
            // 反吹反馈停止，启动延迟恢复定时器
            m_backflowDelayTime = getBackflowDelayTime();
            m_backflowDelayTimer->start(m_backflowDelayTime * 1000);  // 转换为毫秒
            debug_printf("监控系统: 检测到反吹反馈停止，将在%d秒后恢复氧气和一氧化碳数值更新\n", m_backflowDelayTime);
        }
    }
}

void MonitoringDataSource::suspendO2COUpdates()
{
    if (!m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = true;

        // 停止延迟恢复定时器（如果正在运行）
        if (m_backflowDelayTimer->isActive()) {
            m_backflowDelayTimer->stop();
        }

        // 保存当前的O2和CO数值作为暂停前的最后数值
        // 注意：这里的数值会在checkBackflowStatus调用之前通过updateSmokeData获取到
        debug_printf("监控系统: 氧气和一氧化碳数值更新已暂停，保存的暂停前数值 - O2: %s, CO: %s\n",
                    m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
    }
}

void MonitoringDataSource::resumeO2COUpdates()
{
    if (m_isDataUpdateSuspended) {
        m_isDataUpdateSuspended = false;
        debug_printf("监控系统: 延迟%d秒后，氧气和一氧化碳数值更新已恢复\n", m_backflowDelayTime);

        // 立即触发一次数据更新，以显示最新的氧气和一氧化碳数值
        updateData();
    }
}

void MonitoringDataSource::clearData()
{
    // 注意：CSV数据不需要清空，因为数据存储在文件中
    // 只清空表格数据和UI状态
    m_smokeTableData.clear();
    m_dataCount = 0;
    m_tableUpdateCounter = 0;  // 重置表格更新计数器

    // 重置相对时间轴的开始时间
    m_dataStartTimeSet = false;

    emit smokeDataChanged();
    emit smokeTableDataChanged();
    // 移除 chartDataUpdated 信号，改为QML主动请求模式

    debug_printf("数据已清空（CSV文件数据保留）\n");
}

// ==================== 重构：从CSV文件读取数据 ====================

QVariantList MonitoringDataSource::smokeO2Data() const
{
    return readO2DataFromCsv();
}

QVariantList MonitoringDataSource::smokeCOData() const
{
    return readCODataFromCsv();
}

QVariantList MonitoringDataSource::smokeSwitch1Data() const
{
    return readSwitch1DataFromCsv();
}

// 从CSV读取O2数据的私有方法
QVariantList MonitoringDataSource::readO2DataFromCsv() const
{
    if (m_currentBoiler.isEmpty()) {
        return QVariantList();
    }

    // 使用优化的CSV读取方法
    QVariantList csvData = m_csvReader->readRecentChartData(m_currentBoiler, DEFAULT_RECENT_POINTS);
    QVariantList result;

    // 提取O2数据
    for (const QVariant &item : csvData) {
        QVariantMap dataPoint = item.toMap();
        QVariantMap o2Point;
        o2Point["x"] = dataPoint["x"];
        o2Point["x_minutes"] = dataPoint["x_minutes"];
        o2Point["y"] = dataPoint["o2"];
        result.append(o2Point);
    }

    return result;
}

// 从CSV读取CO数据的私有方法
QVariantList MonitoringDataSource::readCODataFromCsv() const
{
    if (m_currentBoiler.isEmpty()) {
        return QVariantList();
    }

    // 使用优化的CSV读取方法
    QVariantList csvData = m_csvReader->readRecentChartData(m_currentBoiler, DEFAULT_RECENT_POINTS);
    QVariantList result;

    // 提取CO数据
    for (const QVariant &item : csvData) {
        QVariantMap dataPoint = item.toMap();
        QVariantMap coPoint;
        coPoint["x"] = dataPoint["x"];
        coPoint["x_minutes"] = dataPoint["x_minutes"];
        coPoint["y"] = dataPoint["co"];
        result.append(coPoint);
    }

    return result;
}

// 从CSV读取Switch1数据的私有方法（保持兼容性，实际可能不需要）
QVariantList MonitoringDataSource::readSwitch1DataFromCsv() const
{
    // Switch1数据在CSV中可能不存在，返回空列表或模拟数据
    return QVariantList();
}

// 数据窗口维护方法已移除，现在使用CSV文件存储，无需内存窗口管理

void MonitoringDataSource::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("UI数据更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();
    m_dataCount++;

    lastUpdateTime = currentTime;
}

void MonitoringDataSource::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    int switch1 = 1;  // 开关量信号，默认关闭状态
    bool hardwareConnected = false;

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    std::string deviceName = m_currentBoiler.toStdString();

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->fd >= 0) {
        hardwareConnected = true;

        // 立即获取数据，不等待data_ready标志
        // 这样可以让UI更快地显示已采集到的参数值
        try {
            get_realtime_data(deviceName, &co, &o2, &current, &voltage, &temperature, &switch1);

            // 在检查反吹状态之前，先保存当前的O2和CO数值（作为潜在的暂停前数值）
            if (!m_isDataUpdateSuspended) {
                // 只在数据更新未暂停时更新暂停前的数值
                m_suspendedO2Value = QString::number(o2, 'f', 2) + "%";
                m_suspendedCOValue = QString::number(co, 'f', 0) + "ppm";
            }

            // 检查反吹反馈状态并处理氧气和一氧化碳数值更新控制
            checkBackflowStatus(switch1);

            // 如果数据更新被暂停，记录日志
            if (m_isDataUpdateSuspended) {
                debug_printf("监控系统: 反吹反馈运行中，氧气和一氧化碳数值保持暂停状态 - O2: %s, CO: %s\n",
                            m_suspendedO2Value.toStdString().c_str(), m_suspendedCOValue.toStdString().c_str());
            }

            // 如果是首次获取到有效数据，记录日志
            if (!it->second->data_ready && (o2 > 0 || co > 0)) {
                debug_printf("UI开始显示烟气分析仪 %s 的部分数据\n", deviceName.c_str());
            }
        } catch (...) {
            // 如果数据采集函数出现异常，设置为无效数据
            debug_printf("烟气数据获取异常: %s\n", deviceName.c_str());
            o2 = co = current = voltage = temperature = 0.0f;
            switch1 = 0;  // 异常时设置为停止状态
        }
    }
#endif

    // 更新连接状态
    if (hardwareConnected) {
        // 硬件连接时直接使用真实数据，不做额外的有效性判断
        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "烟气分析仪已连接";
            debug_printf("UI连接状态: 烟气分析仪已连接\n");
            emit dataConnectionChanged();
        }


        // 获取当前时间
        QDateTime now = QDateTime::currentDateTime();

        // 如果是第一次采集数据，设置开始时间
        if (!m_dataStartTimeSet) {
            m_dataStartTime = now;
            m_dataStartTimeSet = true;
        }

        // 获取当前锅炉的采集间隔配置
        int collectionIntervalSeconds = 15; // 默认15秒
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        extern ConfigManager* g_config_manager;

        std::string boilerName = m_currentBoiler.toStdString();
        auto it = boiler_map.find(boilerName);
        if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
            collectionIntervalSeconds = it->second->collection_interval;
        } else if (g_config_manager && g_config_manager->exists(boilerName, "CollectionInterval")) {
            collectionIntervalSeconds = g_config_manager->get<int>(boilerName, "CollectionInterval", 15);
        }
#endif

        // 使用采集间隔（避免编译器警告）
        Q_UNUSED(collectionIntervalSeconds);

        // 计算基于数据开始时间的相对时间
        // 确保时间轴从0开始，并且严格按照实际时间流逝计算
        QDateTime currentTime = QDateTime::currentDateTime();
        if (!m_dataStartTimeSet) {
            m_dataStartTime = currentTime;
            m_dataStartTimeSet = true;
            debug_printf("数据开始时间已设置: %s\n", m_dataStartTime.toString("yyyy-MM-dd HH:mm:ss").toStdString().c_str());
        }

        // 计算从数据开始到现在的实际时间差
        qint64 elapsedSeconds = m_dataStartTime.secsTo(currentTime);
        double relativeTimeHours = elapsedSeconds / 3600.0;  // 转换为小时
        double relativeTimeMinutes = elapsedSeconds / 60.0;  // 转换为分钟

        // 注意：数据添加已移除，现在数据由csvfile.cpp直接写入CSV文件
        // 图表数据从CSV文件读取，无需在此处添加到内存结构
        debug_printf("数据采集完成 - 时间=%.3f小时(%.1f分钟), O2=%.2f%%, CO=%.0fppm (数据已由CSV文件管理)\n",
                    relativeTimeHours, relativeTimeMinutes, o2, co);

        // 更新当前数据值
        m_currentTemperature = QString::number(temperature, 'f', 1) + "℃";
        m_currentVoltage = QString::number(voltage, 'f', 4) + "kPa";  // 压力值保留4位小数
        m_currentCurrent = QString::number(current, 'f', 3) + "A";

        // 增加表格更新计数器
        m_tableUpdateCounter++;

        // 每三次采集才更新一次表格数据
        if (m_tableUpdateCounter >= TABLE_UPDATE_INTERVAL) {
            m_tableUpdateCounter = 0;  // 重置计数器

            // 添加表格数据（每三次采集更新一次，但在反吹期间使用暂停前的O2和CO数值）
            if (m_isDataUpdateSuspended) {
                // 反吹期间：使用暂停前的O2和CO数值，其他参数使用实时数值
                // 从暂停的数值字符串中提取数字部分
                QString suspendedO2Str = m_suspendedO2Value;
                QString suspendedCOStr = m_suspendedCOValue;
                suspendedO2Str.remove("%");
                suspendedCOStr.remove("ppm");

                double suspendedO2 = suspendedO2Str.toDouble();
                double suspendedCO = suspendedCOStr.toDouble();

                addSmokeTableRow(suspendedO2, suspendedCO, temperature, voltage, current, switch1);
                debug_printf("监控系统: 反吹反馈运行中，表格数据使用暂停前的O2=%.2f, CO=%.0f (每3次采集更新)\n", suspendedO2, suspendedCO);
            } else {
                // 正常期间：使用实时数值
                addSmokeTableRow(o2, co, temperature, voltage, current, switch1);
                debug_printf("监控系统: 表格数据已更新 - O2=%.2f%%, CO=%.0fppm (每3次采集更新)\n", o2, co);
            }

            // 更新Redis缓存
            preGenerateAllTimeRangeCaches();
        } else {
            debug_printf("监控系统: 表格更新计数器=%d/%d，跳过本次表格更新\n", m_tableUpdateCounter, TABLE_UPDATE_INTERVAL);
        }
    } else {
        // 硬件未连接
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "烟气分析仪未连接";
            debug_printf("UI连接状态: 烟气分析仪未连接\n");
            emit dataConnectionChanged();
        }

        // 硬件未连接时设置默认值
        m_currentTemperature = "0.0℃";
        m_currentVoltage = "0.0000kPa";
        m_currentCurrent = "0.000A";
        // 不添加任何数据到图表和表格
    }

    emit smokeDataChanged();
    emit currentDataChanged();

    // 移除主动信号发射，改为QML主动请求模式
    // QML会定时主动调用更新方法，无需C++端推送信号

    // 调试信息：输出当前状态（CSV模式）
    debug_printf("监控系统状态: 硬件连接=%s, 数据连接=%s, 表格数据行数=%d, 表格计数器=%d/%d\n",
                hardwareConnected ? "是" : "否",
                m_isDataConnected ? "是" : "否",
                m_smokeTableData.size(),
                m_tableUpdateCounter,
                TABLE_UPDATE_INTERVAL);
}

void MonitoringDataSource::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();
    emit currentBoilerChanged();
#else
    // 如果硬件数据采集被禁用，提供默认烟气分析仪列表
    m_boilerList << "SmokeAnalyzer1" << "SmokeAnalyzer2";
    if (m_currentBoiler.isEmpty()) {
        m_currentBoiler = "SmokeAnalyzer1";
    }
    emit boilerListChanged();
    emit currentBoilerChanged();
#endif
}


void MonitoringDataSource::addSmokeTableRow(double o2, double co, double temperature, double voltage, double current, int switch1)
{
    QVariantMap row;
    row["time"] = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    row["o2"] = QString::number(o2, 'f', 3);
    row["co"] = QString::number(co, 'f', 0);
    row["temperature"] = QString::number(temperature, 'f', 1);
    row["voltage"] = QString::number(voltage, 'f', 1);
    row["current"] = QString::number(current, 'f', 3);
    row["switch1"] = QString::number(switch1);

    m_smokeTableData.prepend(row);

    // 保持最多5行数据
    if (m_smokeTableData.size() > MAX_TABLE_ROWS) {
        m_smokeTableData.removeLast();
    }

    debug_printf("表格数据已添加: 时间=%s, O2=%.3f%%, CO=%.0fppm, 总行数=%d (每3次采集更新)\n",
                row["time"].toString().toStdString().c_str(),
                o2, co, m_smokeTableData.size());

    emit smokeTableDataChanged();
}


void MonitoringDataSource::updateSmokeChartSeries(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    updateChartFromCsv(o2Series, coSeries, zoomIndex);
}

// 基于CSV数据的图表更新方法（使用Redis缓存优化）
void MonitoringDataSource::updateChartFromCsv(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    static int updateCallCount = 0;

    updateCallCount++;

    if (updateCallCount % 5 == 0) {
        debug_printf("CSV图表更新: updateChartFromCsv被调用, zoomIndex=%d, 锅炉=%s\n",
                    zoomIndex, m_currentBoiler.toStdString().c_str());
    }

    if (!o2Series || !coSeries || m_currentBoiler.isEmpty()) {
        debug_printf("CSV图表更新: 错误 - 参数无效 (o2=%p, co=%p, boiler=%s)\n",
                    o2Series, coSeries, m_currentBoiler.toStdString().c_str());
        return;
    }

    QtCharts::QLineSeries *o2LineSeries = qobject_cast<QtCharts::QLineSeries*>(o2Series);
    QtCharts::QLineSeries *coLineSeries = qobject_cast<QtCharts::QLineSeries*>(coSeries);

    if (!o2LineSeries || !coLineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();

    // 生成包含时间范围的缓存键
    QString cacheKey = generateCacheKey(zoomIndex);

    // 直接从Redis预生成缓存读取数据
    QVariantList cachedCsvData;

    if (m_redisEnabled && m_redisClient && m_redisClient->isConnected()) {
        cachedCsvData = getCachedChartData(cacheKey);

        if (!cachedCsvData.isEmpty()) {
            debug_printf("Redis缓存使用: 时间范围=%d, 数据点数=%d\n", zoomIndex, cachedCsvData.size());
        } else {
            debug_printf("Redis缓存未命中: %s，使用CSV备用数据\n", cacheKey.toStdString().c_str());
            // 备用方案：直接从CSV读取
            cachedCsvData = m_csvReader->readRecentChartData(m_currentBoiler, DEFAULT_RECENT_POINTS);
        }
    } else {
        debug_printf("Redis未连接，直接从CSV读取数据\n");
        cachedCsvData = m_csvReader->readRecentChartData(m_currentBoiler, DEFAULT_RECENT_POINTS);
    }

    if (cachedCsvData.isEmpty()) {
        debug_printf("图表更新: 警告 - 没有数据可显示\n");
        return;
    }

    // 优化：统一使用分钟作为时间轴，通过缩放实现不同时间范围
    QString timeKey = "x_minutes";  // 统一使用分钟轴

    // 直接使用所有可用数据，时间范围通过图表的X轴范围控制
    QVariantList filteredData = cachedCsvData;

    debug_printf("图表数据: zoomIndex=%d, 统一时间轴=分钟, 数据点数=%d\n",
                zoomIndex, filteredData.size());

    QVariantList csvData = filteredData;

    // 预分配内存并在作用域结束时自动释放
    QVector<QPointF> o2Points;
    QVector<QPointF> coPoints;
    o2Points.reserve(qMin(csvData.size(), 2000));  // 限制最大点数，避免内存过度使用
    coPoints.reserve(qMin(csvData.size(), 2000));

    // 计算当前时间范围的X轴限制
    double currentMaxTime = 0.0;
    for (const QVariant &item : csvData) {
        QVariantMap dataPoint = item.toMap();
        double x = dataPoint[timeKey].toDouble();
        if (x > currentMaxTime) {
            currentMaxTime = x;
        }
    }

    // 根据缩放级别计算显示范围（分钟）
    double displayRangeMinutes;
    switch(zoomIndex) {
        case 0: displayRangeMinutes = 24 * 60; break;  // 24小时 = 1440分钟
        case 1: displayRangeMinutes = 12 * 60; break;  // 12小时 = 720分钟
        case 2: displayRangeMinutes = 8 * 60; break;   // 8小时 = 480分钟
        case 3: displayRangeMinutes = 60; break;       // 1小时 = 60分钟
        default: displayRangeMinutes = 24 * 60; break;
    }

    double minDisplayTime = qMax(0.0, currentMaxTime - displayRangeMinutes);

    // 只添加在显示范围内的数据点
    for (const QVariant &item : csvData) {
        QVariantMap dataPoint = item.toMap();
        double x = dataPoint[timeKey].toDouble();
        double o2 = dataPoint["o2"].toDouble();
        double co = dataPoint["co"].toDouble();

        // 只显示在时间范围内的数据
        if (x >= minDisplayTime) {
            o2Points.append(QPointF(x, o2));
            coPoints.append(QPointF(x, co));
        }
    }

    // 使用replace方法，Qt会自动清理旧数据
    o2LineSeries->replace(o2Points);
    coLineSeries->replace(coPoints);

    // 显式清理临时向量（虽然作用域结束时会自动清理，但在32位环境下主动清理更安全）
    o2Points.clear();
    coPoints.clear();
    o2Points.squeeze();  // 释放多余的内存
    coPoints.squeeze();

    // 减少调试输出频率
    if (updateCallCount % 10 == 0) {
        debug_printf("优化图表更新: 原始点数=%d, 显示点数=%d, zoomIndex=%d, 范围=%.1f分钟\n",
                    csvData.size(), o2Points.size(), zoomIndex, displayRangeMinutes);
        if (!o2Points.isEmpty()) {
            debug_printf("显示时间范围: %.1f - %.1f 分钟\n", o2Points.first().x(), o2Points.last().x());
        }
    }


}

void MonitoringDataSource::updateSmokeChartSeriesWithMinutes(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries)
{
    debug_printf("监控系统: updateSmokeChartSeriesWithMinutes被调用，锅炉=%s\n", m_currentBoiler.toStdString().c_str());

    // 简化版本：直接使用CSV数据更新，zoomIndex=3表示1小时视图
    updateChartFromCsv(o2Series, coSeries, 3);
}

void MonitoringDataSource::updateSmokeChartSeriesWithScroll(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex, double scrollOffset)
{
    // 简化版本：直接使用CSV数据更新，忽略滚动偏移（由CsvReader处理）
    updateChartFromCsv(o2Series, coSeries, zoomIndex);

    // 注意：滚动功能可以在CsvReader中实现，或者在QML层面处理
    debug_printf("CSV滚动图表更新: 缩放级别=%d, 滚动偏移=%.2f\n", zoomIndex, scrollOffset);


}

void MonitoringDataSource::reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler)
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    if (newBoiler.isEmpty()) {
        debug_printf("新烟气分析仪名称为空，跳过串口重新初始化\n");
        return;
    }

    std::string newDeviceName = newBoiler.toStdString();
    debug_printf("开始为烟气分析仪 '%s' 重新初始化串口连接\n", newDeviceName.c_str());

    // 查找新烟气分析仪
    auto newIt = boiler_map.find(newDeviceName);
    if (newIt == boiler_map.end() || newIt->second == nullptr) {
        debug_printf("错误: 找不到烟气分析仪 '%s'，设备映射大小: %zu\n", newDeviceName.c_str(), boiler_map.size());
        return;
    }

    Boiler* newDeviceObj = newIt->second;
    debug_printf("找到烟气分析仪 '%s'，当前fd: %d，协议: '%s'\n",
                newDeviceName.c_str(), newDeviceObj->fd, newDeviceObj->protocol.c_str());

    // 检查新烟气分析仪的串口连接状态
    if (newDeviceObj->fd < 0) {
        // 串口未连接，需要重新初始化
        debug_printf("烟气分析仪 '%s' 串口未连接，开始重新初始化串口连接\n", newDeviceName.c_str());

        // 获取配置管理器
        extern ConfigManager* g_config_manager;
        if (g_config_manager == nullptr) {
            debug_printf("错误: 全局配置管理器未初始化\n");
            return;
        }

        try {
            // 重新获取协议配置并初始化串口
            std::string protocol = newDeviceObj->protocol;
            if (protocol.empty()) {
                // 如果协议为空，从配置文件重新读取
                protocol = g_config_manager->get<std::string>(newDeviceName, "Protocol");
                newDeviceObj->protocol = protocol;
                debug_printf("从配置文件重新读取烟气分析仪 '%s' 的协议: '%s'\n", newDeviceName.c_str(), protocol.c_str());
            }

            // 获取协议对应的串口配置
            std::string port = g_config_manager->get<std::string>(protocol, "Port");
            int baud_rate = g_config_manager->get<int>(protocol, "BaudRate", 9600);
            char parity = g_config_manager->get<char>(protocol, "Parity");
            int stop_bits = g_config_manager->get<int>(protocol, "StopBits", 1);
            int data_bits = g_config_manager->get<int>(protocol, "DataBits", 8);

            debug_printf("重新初始化烟气分析仪 '%s' 串口配置:\n", newDeviceName.c_str());
            debug_printf("  协议: '%s'\n", protocol.c_str());
            debug_printf("  端口: '%s'\n", port.c_str());
            debug_printf("  波特率: %d\n", baud_rate);
            debug_printf("  校验位: %c\n", parity);
            debug_printf("  停止位: %d\n", stop_bits);
            debug_printf("  数据位: %d\n", data_bits);

            // 重新打开串口
            extern int open_serial_port(const char *device, int speed, char parity, int stop_bits, int data_bits);
            int new_fd = open_serial_port(port.c_str(), baud_rate, parity, stop_bits, data_bits);

            if (new_fd >= 0) {
                newDeviceObj->fd = new_fd;
                debug_printf("烟气分析仪 '%s' 串口重新初始化成功，文件描述符: %d\n", newDeviceName.c_str(), new_fd);
            } else {
                debug_printf("烟气分析仪 '%s' 串口重新初始化失败\n", newDeviceName.c_str());
            }
        } catch (const std::exception& e) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生异常: %s\n", newDeviceName.c_str(), e.what());
        } catch (...) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生未知异常\n", newDeviceName.c_str());
        }
    } else {
        debug_printf("烟气分析仪 '%s' 串口已连接，文件描述符: %d\n", newDeviceName.c_str(), newDeviceObj->fd);
    }

    // 输出切换完成信息
    if (!oldBoiler.isEmpty()) {
        debug_printf("烟气分析仪切换完成: 从 '%s' 切换到 '%s'\n", oldBoiler.toStdString().c_str(), newDeviceName.c_str());
    } else {
        debug_printf("初始化烟气分析仪 '%s' 完成\n", newDeviceName.c_str());
    }

#else
    debug_printf("硬件数据采集被禁用，跳过串口重新初始化\n");
#endif
}

void MonitoringDataSource::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("调试: 没有选择锅炉，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
    }

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
        m_timer->setInterval(uiInterval);
        debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒，与采集间隔保持一致
            m_timer->setInterval(uiInterval);
            debug_printf("设置UI更新间隔: %d毫秒 (采集间隔: %d秒)\n", uiInterval, collectionInterval);
            return;
        }
    }
#endif
}

void MonitoringDataSource::updateChartIncremental(QtCharts::QAbstractSeries *o2Series, QtCharts::QAbstractSeries *coSeries, int zoomIndex)
{
    // 简化版本：直接使用CSV数据更新
    updateChartFromCsv(o2Series, coSeries, zoomIndex);

    static int updateCount = 0;
    updateCount++;
    if (updateCount % 20 == 0) {
        debug_printf("CSV增量图表更新: 第%d次调用, zoomIndex=%d\n", updateCount, zoomIndex);
    }
}

// ==================== Redis缓存相关实现 ====================

void MonitoringDataSource::initializeRedis()
{
    loadRedisConfig();

    if (!m_redisEnabled) {
        debug_printf("Redis: 缓存已禁用，系统将无法正常工作！\n");
        return;
    }

    m_redisClient = new RedisClient(this);

    // 连接Redis信号
    connect(m_redisClient, &RedisClient::connected, this, [this]() {
        debug_printf("Redis: 连接成功 %s:%d 数据库%d - 纯Redis缓存模式\n",
                    m_redisHost.toStdString().c_str(), m_redisPort, m_redisDatabase);
        emit redisConnectionChanged();
    });

    connect(m_redisClient, &RedisClient::disconnected, this, [this]() {
        debug_printf("Redis: 连接断开 - 图表功能将受影响\n");
        emit redisConnectionChanged();
    });

    connect(m_redisClient, &RedisClient::error, this, [this](const QString &errorMsg) {
        debug_printf("Redis: 错误 - %s\n", errorMsg.toStdString().c_str());
    });

    // 尝试连接Redis（必须成功）
    if (!m_redisClient->connectToRedis(m_redisHost, m_redisPort, m_redisDatabase, m_redisPassword)) {
        debug_printf("Redis: 连接失败！纯Redis模式下系统无法正常工作\n");
        // 不再设置m_redisEnabled = false，保持Redis模式
    } else {
        debug_printf("Redis: 初始化完成，进入纯Redis缓存模式\n");
    }
}

void MonitoringDataSource::loadRedisConfig()
{
    if (!g_config_manager) {
        debug_printf("Redis: 配置管理器未初始化\n");
        return;
    }

    // 读取Redis配置
    m_redisEnabled = g_config_manager->get<bool>("Redis", "Enabled", false);
    m_redisHost = QString::fromStdString(g_config_manager->get<std::string>("Redis", "Host", "127.0.0.1"));
    m_redisPort = g_config_manager->get<int>("Redis", "Port", 6379);
    m_redisPassword = QString::fromStdString(g_config_manager->get<std::string>("Redis", "Password", ""));
    m_redisDatabase = g_config_manager->get<int>("Redis", "Database", 0);
    m_redisCacheExpireHours = g_config_manager->get<int>("Redis", "CacheExpireHours", 24);

    debug_printf("Redis配置: 启用=%s, 地址=%s:%d, 密码=%s, 数据库=%d, 过期时间=%d小时\n",
                m_redisEnabled ? "是" : "否",
                m_redisHost.toStdString().c_str(),
                m_redisPort,
                m_redisPassword.isEmpty() ? "无" : "已设置",
                m_redisDatabase,
                m_redisCacheExpireHours);
}

QString MonitoringDataSource::generateCacheKey(int zoomIndex) const
{
    Q_UNUSED(zoomIndex); // 不再使用zoomIndex，所有视图共享一个缓存键
    QDate today = QDate::currentDate();
    return QString("chart_data_%1_%2").arg(m_currentBoiler, today.toString("yyyyMMdd"));
}

void MonitoringDataSource::preGenerateAllTimeRangeCaches()
{
    if (!m_redisEnabled || !m_redisClient || !m_redisClient->isConnected() || m_currentBoiler.isEmpty()) {
        return;
    }

    // 从CSV读取最新数据
    QVariantList csvData = m_csvReader->readRecentChartData(m_currentBoiler, DEFAULT_RECENT_POINTS);

    if (csvData.isEmpty()) {
        return;
    }

    // 存储数据到Redis，时间范围通过缩放实现
    QString cacheKey = QString("chart_data_%1_%2").arg(m_currentBoiler, QDate::currentDate().toString("yyyyMMdd"));
    setCachedChartData(cacheKey, csvData);
}


bool MonitoringDataSource::setCachedChartData(const QString &key, const QVariantList &data)
{
    if (m_redisEnabled && m_redisClient && m_redisClient->isConnected()) {
        int expireSeconds = m_redisCacheExpireHours * 3600;
        bool success = m_redisClient->setChartData(key, data, expireSeconds);
        if (success) {
            debug_printf("Redis缓存: 成功存储数据，键=%s, 数据点数=%d\n",
                        key.toStdString().c_str(), data.size());
        } else {
            debug_printf("Redis缓存: 存储失败，键=%s\n", key.toStdString().c_str());
        }
        return success;
    } else {
        debug_printf("Redis缓存: 未连接，无法存储数据，键=%s\n", key.toStdString().c_str());
        return false;
    }
}

QVariantList MonitoringDataSource::getCachedChartData(const QString &key)
{
    if (m_redisEnabled && m_redisClient && m_redisClient->isConnected()) {
        QVariantList data = m_redisClient->getChartData(key);
        if (!data.isEmpty()) {
            debug_printf("Redis缓存: 成功读取数据，键=%s, 数据点数=%d\n",
                        key.toStdString().c_str(), data.size());
        } else {
            debug_printf("Redis缓存: 数据为空或不存在，键=%s\n", key.toStdString().c_str());
        }
        return data;
    } else {
        debug_printf("Redis缓存: 未连接，无法读取数据，键=%s\n", key.toStdString().c_str());
        return QVariantList();
    }
}

bool MonitoringDataSource::hasCachedChartData(const QString &key)
{
    if (m_redisEnabled && m_redisClient && m_redisClient->isConnected()) {
        bool exists = m_redisClient->hasChartData(key);
        debug_printf("Redis缓存: 检查数据存在性，键=%s, 存在=%s\n",
                    key.toStdString().c_str(), exists ? "是" : "否");
        return exists;
    } else {
        debug_printf("Redis缓存: 未连接，无法检查数据存在性，键=%s\n", key.toStdString().c_str());
        return false;
    }
}

bool MonitoringDataSource::isRedisConnected() const
{
    return m_redisEnabled && m_redisClient && m_redisClient->isConnected();
}


